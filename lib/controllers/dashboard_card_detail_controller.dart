import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/order_model.dart';
import '../models/country_model.dart';
import '../widgets/edit_order_dialog.dart';

class DashboardCardDetailController extends GetxController {
  // Observable variables
  final RxList<OrderModel> orders = <OrderModel>[].obs;
  final RxList<OrderModel> filteredOrders = <OrderModel>[].obs;
  final RxString searchQuery = ''.obs;
  final RxInt selectedNavIndex = 0.obs; // Dashboard is index 0
  final RxBool isLoading = false.obs;
  final RxString cardTitle = ''.obs;
  final RxInt cardCount = 0.obs;

  // Controllers
  final TextEditingController searchController = TextEditingController();

  // Edit Order Dialog Controllers
  final TextEditingController editCustomerNameController = TextEditingController();
  final TextEditingController editMobileNumberController = TextEditingController();
  final TextEditingController editDeliveryDateController = TextEditingController();
  final TextEditingController editOrderStatusController = TextEditingController();
  final TextEditingController editOrderAmountController = TextEditingController();
  final TextEditingController editTrackingIdController = TextEditingController();
  final TextEditingController editStreetAddressController = TextEditingController();
  final TextEditingController editCityController = TextEditingController();
  final TextEditingController editStateController = TextEditingController();
  final TextEditingController editPinCodeController = TextEditingController();

  // Edit Order Observable Variables
  final Rx<CountryModel> editSelectedCountry = CountryModel(
    name: 'India',
    code: 'IN',
    dialCode: '+91',
    flag: '🇮🇳',
  ).obs;

  @override
  void onInit() {
    super.onInit();

    // Get parameters from route
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      cardTitle.value = arguments['title'] ?? '';
      cardCount.value = arguments['count'] ?? 0;
      loadOrdersForCard(cardTitle.value, cardCount.value);
    }

    // Listen to search changes
    searchController.addListener(() {
      searchQuery.value = searchController.text;
      onSearchChanged(searchController.text);
    });
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  // Load orders based on card type
  void loadOrdersForCard(String cardType, int count) {
    isLoading.value = true;

    // Generate dummy orders based on card type
    List<OrderModel> dummyOrders = _generateDummyOrders(cardType, count);

    orders.value = dummyOrders;
    filteredOrders.value = dummyOrders;
    isLoading.value = false;
  }

  // Generate dummy orders based on card type
  List<OrderModel> _generateDummyOrders(String cardType, int count) {
    List<OrderModel> dummyOrders = [];

    for (int i = 1; i <= count; i++) {
      String orderId = 'ORD-${(i.toString().padLeft(3, '0'))}';

      // Different customer names and data for variety
      List<String> customerNames = ['Chaitanya', 'Kumar', 'Sirja', 'Priya', 'Rohit', 'Anita', 'Vikash', 'Neha'];
      List<String> phoneNumbers = ['+91 8520260809', '+91 7841759312', '+91 3367771523', '+91 7345681991'];
      List<double> prices = [3000, 4000, 9000, 7000, 5000, 6000];

      dummyOrders.add(OrderModel(
        orderId: orderId,
        customerName: customerNames[i % customerNames.length],
        customerPhone: phoneNumbers[i % phoneNumbers.length],
        orderDate: DateTime.now().subtract(Duration(days: i)),
        deliveryDate: DateTime.now().add(Duration(days: 7 - i)),
        itemCount: (i % 5) + 1, // 1-5 items
        amount: prices[i % prices.length],
        status: cardType,
      ));
    }

    return dummyOrders;
  }

  // Handle search
  void onSearchChanged(String query) {
    searchQuery.value = query;
    if (query.isEmpty) {
      filteredOrders.value = orders;
    } else {
      filteredOrders.value = orders.where((order) {
        return order.orderId.toLowerCase().contains(query.toLowerCase()) ||
               order.customerName.toLowerCase().contains(query.toLowerCase()) ||
               order.customerPhone.contains(query);
      }).toList();
    }
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard
        Get.offNamed('/dashboard');
        break;
      case 1:
        // Customers List
        Get.offNamed('/customers-list');
        break;
      case 2:
        // Orders List
        Get.offNamed('/order-list');
        break;
    }
  }

  // Handle back to dashboard
  void onBackToDashboard() {
    Get.back();
  }

  // Handle add order
  void onAddOrderTap() {
    Get.toNamed('/add-order');
  }

  // Handle order actions
  void onViewDetails(OrderModel order) {
    Get.snackbar(
      'View Details',
      'Viewing details for ${order.orderId}',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void onEditOrder(OrderModel order) {
    _showEditOrderDialog(order);
  }

  // Show Edit Order Dialog
  void _showEditOrderDialog(OrderModel order) {
    _prefillEditForm(order);
    Get.dialog(
      EditOrderDialog(
        order: order,
        onSave: (updatedOrder) => _handleOrderUpdate(updatedOrder),
      ),
      barrierDismissible: false,
    );
  }

  // Prefill edit form with existing order data
  void _prefillEditForm(OrderModel order) {
    editCustomerNameController.text = order.customerName;
    editMobileNumberController.text = order.customerPhone.replaceAll('+91', '').trim();
    editDeliveryDateController.text = _formatDateForInput(order.deliveryDate);
    editOrderStatusController.text = order.status;
    editOrderAmountController.text = order.amount.toString();
    editTrackingIdController.text = 'TRK${order.orderId.replaceAll('ORD-', '')}';

    // Mock address data - in real app this would come from order model
    editStreetAddressController.text = '123 Business Street';
    editCityController.text = 'Mumbai';
    editStateController.text = 'Mumbai';
    editPinCodeController.text = 'Mumbai';
  }

  // Handle order update
  void _handleOrderUpdate(OrderModel updatedOrder) {
    // Update the order in the local list
    final orderIndex = orders.indexWhere((o) => o.orderId == updatedOrder.orderId);
    if (orderIndex != -1) {
      orders[orderIndex] = updatedOrder;

      // Also update filtered orders if they contain this order
      final filteredIndex = filteredOrders.indexWhere((o) => o.orderId == updatedOrder.orderId);
      if (filteredIndex != -1) {
        filteredOrders[filteredIndex] = updatedOrder;
      }
    }

    Get.back(); // Close dialog
    Get.snackbar(
      'Success',
      'Order ${updatedOrder.orderId} updated successfully!',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  // Select country for edit dialog
  void selectEditCountry(CountryModel country) {
    editSelectedCountry.value = country;
  }

  // Format date for input field
  String _formatDateForInput(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  void onUpdateStatus(OrderModel order) async {
    final result = await Get.toNamed('/update-status', arguments: {
      'order': order,
      'sourceScreen': 'dashboard',
    });

    // Handle the updated order data if status was updated
    if (result != null && result['wasUpdated'] == true) {
      final updatedOrder = result['updatedOrder'] as OrderModel;

      // Update the order in the local list
      final orderIndex = orders.indexWhere((o) => o.orderId == updatedOrder.orderId);
      if (orderIndex != -1) {
        orders[orderIndex] = updatedOrder;

        // Also update filtered orders if they contain this order
        final filteredIndex = filteredOrders.indexWhere((o) => o.orderId == updatedOrder.orderId);
        if (filteredIndex != -1) {
          filteredOrders[filteredIndex] = updatedOrder;
        }
      }
    }
  }

  // Get priority for order (dummy logic)
  String getPriority(OrderModel order) {
    // Simple logic: orders with higher amounts get higher priority
    if (order.amount >= 7000) return 'High Priority';
    if (order.amount >= 4000) return 'Medium Priority';
    return 'Low Priority';
  }

  // Get priority color
  Color getPriorityColor(String priority) {
    switch (priority) {
      case 'High Priority':
        return const Color(0xFFFF6B6B);
      case 'Medium Priority':
        return const Color(0xFFFFB347);
      case 'Low Priority':
        return const Color(0xFF4ECDC4);
      default:
        return Colors.grey;
    }
  }

  // Get notes for order (dummy)
  String getNotes(OrderModel order) {
    List<String> notes = [
      'Urgent Delivery Required for Client',
      'Standard delivery timeline acceptable',
      'Client prefers morning delivery',
      'Handle with care - fragile items'
    ];
    return notes[order.orderId.hashCode % notes.length];
  }
}
